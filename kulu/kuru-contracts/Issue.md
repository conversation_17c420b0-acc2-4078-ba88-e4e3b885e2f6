What the spec says vs. what the code does

Spec (NatSpec in deployProxy):

_makerFeeBps — “The maker fee must be lower than the taker fee.”

Implementation: No check enforces that. In deployProxy(...) you validate market type, tickSize, and the 10^n precisions—but never compare _makerFeeBps and _takerFeeBps.

So today you can deploy a market with makerFeeBps >= takerFeeBps. The Router will:

write it to verifiedMarket,

call MarginAccount.updateMarkets(proxy),

grant unlimited approvals,

and initialize the OrderBook with those fees.

Why it matters (validity & impact)

Spec mismatch → broken assumptions: If the OB math assumes makerFeeBps < takerFeeBps (common for fee-spread/rebate logic), you risk:

Underflow on expressions like takerFeeBps - makerFeeBps when using unsigned math.

Negative rebates / inverted economics: makers paying more than takers, wrecking incentives and routing logic.

Invariant drift: any “maker ≤ taker” invariant in fee splitting, vault accrual, or AMM spread math can silently fail.

Surface widens via Router: Because Router “verifies” and widely exposes the market, a bad fee config becomes systemic (integrators will trust it).

Minimal patch (Router)

Enforce exactly what your doc promises:

require(_makerFeeBps < _takerFeeBps, RouterErrors.InvalidFeeRelation());


While you’re here, add basic bounds (unless you intentionally allow >100%):

// optional but recommended
require(_makerFeeBps <= 10_000 && _takerFeeBps <= 10_000, RouterErrors.InvalidFeeBps());

Defense-in-depth (OrderBook)

Even if Router checks, also validate in IOrderBook.initialize:

require(makerFeeBps < takerFeeBps, OBErrors.InvalidFeeRelation());


Duplicating this guard prevents misconfigured markets created outside Router or via future upgrades.

Quick tests

Deploy with _makerFeeBps = _takerFeeBps → should revert.

Deploy with _makerFeeBps > _takerFeeBps → should revert.

Fuzz around boundary values (0/1/10_000) to ensure no off-by-one.

Bottom line: The documentation creates a contract (promise) that the code doesn’t uphold. If downstream logic relies on that promise, you can get economic breakage or even arithmetic faults. Add the check in Router (and OB) to make the spec true.